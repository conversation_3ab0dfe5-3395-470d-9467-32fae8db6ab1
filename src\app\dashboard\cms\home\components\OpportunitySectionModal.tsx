'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2 } from 'lucide-react';
import { useUpdateOpportunitySection } from '@/hooks/useMutation';
import { IOpportunitySection, IOpportunityStep } from '@/types/query.types';

interface OpportunitySectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IOpportunitySection;
}

interface FormData {
  heading: string;
  description: string;
  image: string;
  steps: IOpportunityStep[];
}

export function OpportunitySectionModal({
  isOpen,
  onClose,
  data
}: OpportunitySectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    image: '',
    steps: []
  });

  const { mutate: updateSection, isPending } = useUpdateOpportunitySection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        image: data.image || '',
        steps:
          data.steps?.map((step) => ({
            heading: step.heading,
            description: step.description
          })) || []
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleStepChange = (
    index: number,
    field: keyof IOpportunityStep,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      steps: prev.steps.map((step, i) =>
        i === index ? { ...step, [field]: value } : step
      )
    }));
  };

  const addStep = () => {
    setFormData((prev) => ({
      ...prev,
      steps: [...prev.steps, { heading: '', description: '' }]
    }));
  };

  const removeStep = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      steps: prev.steps.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    // Validate steps
    const validSteps = formData.steps.filter(
      (step) => step.heading.trim() && step.description.trim()
    );

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      image: formData.image.trim(),
      steps: validSteps
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl'>
        <DialogHeader>
          <DialogTitle>Edit Opportunity Section</DialogTitle>
          <DialogDescription>
            Update the opportunity section content including heading,
            description, image, and steps.
          </DialogDescription>
        </DialogHeader>

        <form className='block' onSubmit={handleSubmit}>
          <ScrollArea className='h-[60vh] pr-4'>
            <div className='space-y-6'>
              {/* Basic Fields */}
              <div className='space-y-4'>
                <div className='space-y-3'>
                  <Label htmlFor='heading'>Heading *</Label>
                  <Input
                    id='heading'
                    value={formData.heading}
                    onChange={(e) =>
                      handleInputChange('heading', e.target.value)
                    }
                    placeholder='Enter section heading'
                    required
                  />
                </div>

                <div className='space-y-3'>
                  <Label htmlFor='description'>Description *</Label>
                  <Textarea
                    id='description'
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange('description', e.target.value)
                    }
                    placeholder='Enter section description'
                    rows={3}
                    required
                  />
                </div>

                <div className='space-y-3'>
                  <Label htmlFor='image'>Image URL</Label>
                  <Input
                    id='image'
                    value={formData.image}
                    onChange={(e) => handleInputChange('image', e.target.value)}
                    placeholder='Enter image URL'
                    type='url'
                  />
                </div>
              </div>

              {/* Steps Section */}
              <div>
                <div className='mb-4 flex items-center justify-between'>
                  <Label className='text-base font-medium'>Steps</Label>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={addStep}
                  >
                    <Plus className='mr-1 h-4 w-4' />
                    Add Step
                  </Button>
                </div>

                <div className='space-y-4'>
                  {formData.steps.map((step, index) => (
                    <Card key={index} className='border-l-primary border-l-4'>
                      <CardHeader className='pb-3'>
                        <div className='flex items-center justify-between'>
                          <CardTitle className='text-sm'>
                            Step {index + 1}
                          </CardTitle>
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            onClick={() => removeStep(index)}
                            className='text-destructive hover:text-destructive'
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className='space-y-3'>
                        <div>
                          <Label htmlFor={`step-heading-${index}`}>
                            Step Heading
                          </Label>
                          <Input
                            id={`step-heading-${index}`}
                            value={step.heading}
                            onChange={(e) =>
                              handleStepChange(index, 'heading', e.target.value)
                            }
                            placeholder='Enter step heading'
                          />
                        </div>
                        <div>
                          <Label htmlFor={`step-description-${index}`}>
                            Step Description
                          </Label>
                          <Textarea
                            id={`step-description-${index}`}
                            value={step.description}
                            onChange={(e) =>
                              handleStepChange(
                                index,
                                'description',
                                e.target.value
                              )
                            }
                            placeholder='Enter step description'
                            rows={2}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {formData.steps.length === 0 && (
                    <div className='text-muted-foreground py-8 text-center'>
                      <p>
                        No steps added yet. Click &quot;Add Step&quot; to create
                        your first step.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>
          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
