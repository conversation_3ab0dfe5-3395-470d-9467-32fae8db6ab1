'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

interface ViewSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionType: SectionType;
  data: any;
}

export function ViewSectionModal({
  isOpen,
  onClose,
  sectionType,
  data
}: ViewSectionModalProps) {
  if (!data) return null;

  const getSectionTitle = (type: SectionType) => {
    const titles = {
      opportunity: 'Opportunity Section',
      talent: 'Talent Section',
      discover: 'Discover Section',
      companies: 'Companies Section',
      partners: 'Partners Section',
      cta: 'Call to Action Section',
      app: 'Mobile App Section'
    };
    return titles[type];
  };

  const renderBasicFields = () => (
    <div className='space-y-4'>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Heading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {data.heading}
        </p>
      </div>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Description</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {data.description}
        </p>
      </div>
    </div>
  );

  const renderSteps = () => {
    if (!data.steps || data.steps.length === 0) return null;

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Steps ({data.steps.length})
        </h4>
        <div className='space-y-3'>
          {data.steps.map((step: any, index: number) => (
            <Card
              key={step._id || index}
              className='border-l-primary border-l-4'
            >
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>Step {index + 1}</Badge>
                    <h5 className='font-medium'>{step.heading}</h5>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    {step.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderPartners = () => {
    if (!data.partners || data.partners.length === 0) return null;

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Partners ({data.partners.length})
        </h4>
        <div className='grid grid-cols-1 gap-3 sm:grid-cols-2'>
          {data.partners.map((partner: any, index: number) => (
            <Card key={partner._id || index}>
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <h5 className='font-medium'>{partner.name}</h5>
                  {partner.imageURL && (
                    <div className='text-muted-foreground text-sm'>
                      <span className='font-medium'>Image URL:</span>
                      <p className='bg-muted mt-1 rounded p-2 text-xs break-all'>
                        {partner.imageURL}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderImage = () => {
    if (!data.image) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Image</h4>
        <div className='bg-muted rounded-md p-3'>
          <p className='text-muted-foreground text-sm break-all'>
            {data.image}
          </p>
        </div>
      </div>
    );
  };

  const renderSubheading = () => {
    if (!data.subheading) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Subheading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {data.subheading}
        </p>
      </div>
    );
  };

  const renderAppUrls = () => {
    if (sectionType !== 'app') return null;

    return (
      <div className='space-y-4'>
        <div>
          <h4 className='mb-2 text-sm font-medium'>App Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {data.appStoreURL}
            </p>
          </div>
        </div>
        <div>
          <h4 className='mb-2 text-sm font-medium'>Play Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {data.playStoreURL}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            {getSectionTitle(sectionType)}
            <Badge variant='outline'>{sectionType}</Badge>
          </DialogTitle>
          <DialogDescription>
            View the current content of this section
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className='max-h-[70vh] pr-4'>
          <div className='space-y-6'>
            {renderBasicFields()}

            {renderSubheading()}

            {renderImage()}

            {renderAppUrls()}

            {data.steps && (
              <>
                <Separator />
                {renderSteps()}
              </>
            )}

            {data.partners && (
              <>
                <Separator />
                {renderPartners()}
              </>
            )}

            <Separator />
            <div className='text-muted-foreground space-y-1 text-xs'>
              <p>
                <span className='font-medium'>ID:</span> {data._id}
              </p>
              <p>
                <span className='font-medium'>Version:</span> {data.__v}
              </p>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
